import React from 'react';

export default function About() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">About This Project</h1>
        </div>
        
        <div className="bg-white shadow-lg rounded-lg p-8">
          <div className="prose max-w-none">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">CPAN 144 - Final Exam Project</h2>
            
            <p className="text-gray-600 mb-6">
              This project demonstrates advanced front-end development concepts and techniques 
              covered throughout the course. It showcases various React and Next.js features 
              including component design, state management, event handling, routing, and API integration.
            </p>

            <h3 className="text-xl font-semibold text-gray-800 mb-3">Project Sections</h3>
            
            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Section 1: Component Design</h4>
                <p className="text-blue-700 text-sm">
                  UserProfile component with proper JSX syntax and React conventions
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">Section 2: State Management</h4>
                <p className="text-green-700 text-sm">
                  Counter component with increment, decrement, and reset functionality
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">Section 3: Event Handling</h4>
                <p className="text-purple-700 text-sm">
                  TodoList component with add/remove items and conditional rendering
                </p>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Section 4: Routing</h4>
                <p className="text-orange-700 text-sm">
                  Next.js routing with navigation between Home and About pages
                </p>
              </div>
              
              <div className="bg-red-50 p-4 rounded-lg md:col-span-2">
                <h4 className="font-semibold text-red-800 mb-2">Section 5: APIs and Data Fetching</h4>
                <p className="text-red-700 text-sm">
                  Data fetching from JSONPlaceholder API with error handling and loading states
                </p>
              </div>
            </div>

            <h3 className="text-xl font-semibold text-gray-800 mb-3">Technologies Used</h3>
            <ul className="list-disc list-inside text-gray-600 space-y-1 mb-6">
              <li>Next.js 15.4.6 with App Router</li>
              <li>React 19.1.0 with TypeScript</li>
              <li>Tailwind CSS for styling</li>
              <li>JSONPlaceholder API for data fetching</li>
            </ul>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-800 mb-2">Course Information</h4>
              <p className="text-gray-600 text-sm">
                <strong>Course:</strong> CPAN 144 - Advanced Front-End Programming<br />
                <strong>Semester:</strong> 2<br />
                <strong>Assessment:</strong> Take Home Final Exam
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
