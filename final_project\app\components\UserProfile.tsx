import React from 'react';
import Image from 'next/image';

interface UserProfileProps {
  name: string;
  profilePicture: string;
  bio: string;
}

const UserProfile: React.FC<UserProfileProps> = ({ name, profilePicture, bio }) => {
  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4">
      <div className="md:flex">
        <div className="md:shrink-0">
          <Image
            className="h-48 w-full object-cover md:h-full md:w-48"
            src={profilePicture}
            alt={`${name}'s profile picture`}
            width={192}
            height={192}
            priority
          />
        </div>
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
            User Profile
          </div>
          <h2 className="block mt-1 text-lg leading-tight font-medium text-black">
            {name}
          </h2>
          <p className="mt-2 text-slate-500">
            {bio}
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
