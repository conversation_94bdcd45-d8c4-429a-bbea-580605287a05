{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/Counter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\nconst Counter: React.FC = () => {\n  const [count, setCount] = useState<number>(0);\n\n  const increment = () => {\n    setCount(prevCount => prevCount + 1);\n  };\n\n  const decrement = () => {\n    setCount(prevCount => prevCount - 1);\n  };\n\n  const reset = () => {\n    setCount(0);\n  };\n\n  // Conditional rendering based on counter value\n  const getCounterMessage = () => {\n    if (count === 0) {\n      return \"Counter is at zero\";\n    } else if (count > 0) {\n      return `Counter is positive: ${count}`;\n    } else {\n      return `Counter is negative: ${count}`;\n    }\n  };\n\n  const getCounterColor = () => {\n    if (count === 0) {\n      return \"text-gray-600\";\n    } else if (count > 0) {\n      return \"text-green-600\";\n    } else {\n      return \"text-red-600\";\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-xl shadow-md p-6 m-4\">\n      <h2 className=\"text-2xl font-bold text-center mb-4 text-gray-900\">Counter Component</h2>\n      \n      <div className=\"text-center mb-4\">\n        <div className={`text-4xl font-bold mb-2 ${getCounterColor()}`}>\n          {count}\n        </div>\n        <p className={`text-lg ${getCounterColor()}`}>\n          {getCounterMessage()}\n        </p>\n      </div>\n\n      <div className=\"flex justify-center space-x-4\">\n        <button\n          onClick={decrement}\n          className=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors\"\n        >\n          Decrement\n        </button>\n        <button\n          onClick={reset}\n          className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors\"\n        >\n          Reset\n        </button>\n        <button\n          onClick={increment}\n          className=\"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors\"\n        >\n          Increment\n        </button>\n      </div>\n\n      {/* Additional conditional rendering for special values */}\n      {count === 10 && (\n        <div className=\"mt-4 p-2 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded\">\n          🎉 You've reached 10! Great job!\n        </div>\n      )}\n      \n      {count === -10 && (\n        <div className=\"mt-4 p-2 bg-blue-100 border border-blue-400 text-blue-700 rounded\">\n          ❄️ You've reached -10! That's quite low!\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Counter;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,UAAoB;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,YAAY;QAChB,SAAS,CAAA,YAAa,YAAY;IACpC;IAEA,MAAM,YAAY;QAChB,SAAS,CAAA,YAAa,YAAY;IACpC;IAEA,MAAM,QAAQ;QACZ,SAAS;IACX;IAEA,+CAA+C;IAC/C,MAAM,oBAAoB;QACxB,IAAI,UAAU,GAAG;YACf,OAAO;QACT,OAAO,IAAI,QAAQ,GAAG;YACpB,OAAO,AAAC,wBAA6B,OAAN;QACjC,OAAO;YACL,OAAO,AAAC,wBAA6B,OAAN;QACjC;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,UAAU,GAAG;YACf,OAAO;QACT,OAAO,IAAI,QAAQ,GAAG;YACpB,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAoD;;;;;;0BAElE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,2BAA4C,OAAlB;kCACxC;;;;;;kCAEH,6LAAC;wBAAE,WAAW,AAAC,WAA4B,OAAlB;kCACtB;;;;;;;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMF,UAAU,oBACT,6LAAC;gBAAI,WAAU;0BAA0E;;;;;;YAK1F,UAAU,CAAC,oBACV,6LAAC;gBAAI,WAAU;0BAAoE;;;;;;;;;;;;AAM3F;GApFM;KAAA;uCAsFS", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/TodoList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\ninterface TodoItem {\n  id: number;\n  text: string;\n  completed: boolean;\n}\n\nconst TodoList: React.FC = () => {\n  const [todos, setTodos] = useState<TodoItem[]>([]);\n  const [inputText, setInputText] = useState<string>('');\n\n  const addTodo = () => {\n    if (inputText.trim() !== '') {\n      const newTodo: TodoItem = {\n        id: Date.now(),\n        text: inputText.trim(),\n        completed: false\n      };\n      setTodos(prevTodos => [...prevTodos, newTodo]);\n      setInputText('');\n    }\n  };\n\n  const removeTodo = (id: number) => {\n    setTodos(prevTodos => prevTodos.filter(todo => todo.id !== id));\n  };\n\n  const toggleComplete = (id: number) => {\n    setTodos(prevTodos =>\n      prevTodos.map(todo =>\n        todo.id === id ? { ...todo, completed: !todo.completed } : todo\n      )\n    );\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setInputText(e.target.value);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      addTodo();\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-xl shadow-md p-6 m-4\">\n      <h2 className=\"text-2xl font-bold text-center mb-4 text-gray-900\">Todo List</h2>\n      \n      {/* Input section */}\n      <div className=\"flex mb-4\">\n        <input\n          type=\"text\"\n          value={inputText}\n          onChange={handleInputChange}\n          onKeyPress={handleKeyPress}\n          placeholder=\"Add a new todo...\"\n          className=\"flex-1 px-3 py-2 border-2 border-gray-400 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-500\"\n        />\n        <button\n          onClick={addTodo}\n          className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r-md transition-colors\"\n        >\n          Add\n        </button>\n      </div>\n\n      {/* Conditional rendering for empty state */}\n      {todos.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-600 text-6xl mb-4\">📝</div>\n          <p className=\"text-gray-700 text-lg\">No todos yet!</p>\n          <p className=\"text-gray-600 text-sm\">Add your first todo above to get started.</p>\n        </div>\n      ) : (\n        <div>\n          {/* Todo list */}\n          <ul className=\"space-y-2\">\n            {todos.map(todo => (\n              <li\n                key={todo.id}\n                className={`flex items-center justify-between p-3 border rounded-md ${\n                  todo.completed \n                    ? 'bg-gray-100 border-gray-300' \n                    : 'bg-white border-gray-200'\n                }`}\n              >\n                <div className=\"flex items-center flex-1\">\n                  <input\n                    type=\"checkbox\"\n                    checked={todo.completed}\n                    onChange={() => toggleComplete(todo.id)}\n                    className=\"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span\n                    className={`${\n                      todo.completed \n                        ? 'line-through text-gray-500' \n                        : 'text-gray-900'\n                    }`}\n                  >\n                    {todo.text}\n                  </span>\n                </div>\n                <button\n                  onClick={() => removeTodo(todo.id)}\n                  className=\"ml-3 bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm transition-colors\"\n                >\n                  Remove\n                </button>\n              </li>\n            ))}\n          </ul>\n\n          {/* Todo statistics */}\n          <div className=\"mt-4 text-sm text-gray-700 text-center\">\n            <p>\n              Total: {todos.length} | \n              Completed: {todos.filter(todo => todo.completed).length} | \n              Remaining: {todos.filter(todo => !todo.completed).length}\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TodoList;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUA,MAAM,WAAqB;;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,UAAU;QACd,IAAI,UAAU,IAAI,OAAO,IAAI;YAC3B,MAAM,UAAoB;gBACxB,IAAI,KAAK,GAAG;gBACZ,MAAM,UAAU,IAAI;gBACpB,WAAW;YACb;YACA,SAAS,CAAA,YAAa;uBAAI;oBAAW;iBAAQ;YAC7C,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC7D;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,WAAW,CAAC,KAAK,SAAS;gBAAC,IAAI;IAGjE;IAEA,MAAM,oBAAoB,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC,KAAK;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAoD;;;;;;0BAGlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,YAAY;wBACZ,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMF,MAAM,MAAM,KAAK,kBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;yEAGvC,6LAAC;;kCAEC,6LAAC;wBAAG,WAAU;kCACX,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC;gCAEC,WAAW,AAAC,2DAIX,OAHC,KAAK,SAAS,GACV,gCACA;;kDAGN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,KAAK,SAAS;gDACvB,UAAU,IAAM,eAAe,KAAK,EAAE;gDACtC,WAAU;;;;;;0DAEZ,6LAAC;gDACC,WAAW,AAAC,GAIX,OAHC,KAAK,SAAS,GACV,+BACA;0DAGL,KAAK,IAAI;;;;;;;;;;;;kDAGd,6LAAC;wCACC,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;kDACX;;;;;;;+BA3BI,KAAK,EAAE;;;;;;;;;;kCAmClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCAAE;gCACO,MAAM,MAAM;gCAAC;gCACT,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;gCAAC;gCAC5C,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAOtE;GAvHM;KAAA;uCAyHS", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/PostsList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface Country {\n  name: {\n    common: string;\n    official: string;\n  };\n  capital: string[];\n  region: string;\n  subregion: string;\n  population: number;\n  area: number;\n  flag: string;\n  cca3: string;\n}\n\nconst CountriesList: React.FC = () => {\n  const [countries, setCountries] = useState<Country[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchCountries = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const response = await fetch('https://restcountries.com/v3.1/region/europe?fields=name,capital,region,subregion,population,area,flag,cca3');\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data: Country[] = await response.json();\n\n        // Limit to first 12 countries for better display\n        setCountries(data.slice(0, 12));\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An unknown error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCountries();\n  }, []);\n\n  const retryFetch = () => {\n    setError(null);\n    setLoading(true);\n    // Re-trigger the useEffect by updating a dependency\n    window.location.reload();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4\">\n        <h2 className=\"text-2xl font-bold text-center mb-6\">Countries from REST Countries API</h2>\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          <span className=\"ml-3 text-gray-600\">Loading countries...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4\">\n        <h2 className=\"text-2xl font-bold text-center mb-6\">Countries from REST Countries API</h2>\n        <div className=\"text-center py-12\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold text-red-600 mb-2\">Error Loading Countries</h3>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button\n            onClick={retryFetch}\n            className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4\">\n      <h2 className=\"text-2xl font-bold text-center mb-6\">Countries from REST Countries API</h2>\n\n      <div className=\"mb-4 text-center\">\n        <span className=\"bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded\">\n          ✅ Successfully loaded {countries.length} countries\n        </span>\n      </div>\n\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n        {countries.map((country) => (\n          <div\n            key={country.cca3}\n            className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\"\n          >\n            <div className=\"flex items-start justify-between mb-3\">\n              <span className=\"text-2xl\">{country.flag}</span>\n              <span className=\"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded\">\n                {country.cca3}\n              </span>\n            </div>\n\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              {country.name.common}\n            </h3>\n\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <p><strong>Capital:</strong> {country.capital?.[0] || 'N/A'}</p>\n              <p><strong>Region:</strong> {country.region}</p>\n              <p><strong>Subregion:</strong> {country.subregion}</p>\n              <p><strong>Population:</strong> {country.population.toLocaleString()}</p>\n              <p><strong>Area:</strong> {country.area.toLocaleString()} km²</p>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"mt-6 text-center\">\n        <button\n          onClick={retryFetch}\n          className=\"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors\"\n        >\n          Refresh Countries\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CountriesList;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAkBA,MAAM,gBAA0B;;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;0DAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;wBACxD;wBAEA,MAAM,OAAkB,MAAM,SAAS,IAAI;wBAE3C,iDAAiD;wBACjD,aAAa,KAAK,KAAK,CAAC,GAAG;oBAC7B,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,SAAS;QACT,WAAW;QACX,oDAAoD;QACpD,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAsC;;;;;;8BACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAsC;;;;;;8BACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA6B;;;;;;sCAC5C,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BAEpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;wBAAwE;wBAC/D,UAAU,MAAM;wBAAC;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;wBAiBoB;yCAhBlC,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY,QAAQ,IAAI;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDACb,QAAQ,IAAI;;;;;;;;;;;;0CAIjB,6LAAC;gCAAG,WAAU;0CACX,QAAQ,IAAI,CAAC,MAAM;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAiB;4CAAE,EAAA,mBAAA,QAAQ,OAAO,cAAf,uCAAA,gBAAiB,CAAC,EAAE,KAAI;;;;;;;kDACtD,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAgB;4CAAE,QAAQ,MAAM;;;;;;;kDAC3C,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAmB;4CAAE,QAAQ,SAAS;;;;;;;kDACjD,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAoB;4CAAE,QAAQ,UAAU,CAAC,cAAc;;;;;;;kDAClE,6LAAC;;0DAAE,6LAAC;0DAAO;;;;;;4CAAc;4CAAE,QAAQ,IAAI,CAAC,cAAc;4CAAG;;;;;;;;;;;;;;uBAnBtD,QAAQ,IAAI;;;;;;;;;;;0BAyBvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GArHM;KAAA;uCAuHS", "debugId": null}}]}