# CPAN 144 - Final Exam Project

This project implements all requirements for the CPAN 144 Advanced Front-End Programming final exam, demonstrating comprehensive knowledge of React, Next.js, and modern front-end development practices.

## 📋 Project Requirements Implementation

### Section 1: Component Design and JSX (20%)
✅ **UserProfile Component**
- Displays user's name, profile picture, and bio
- Uses proper JSX syntax and React conventions
- Implements TypeScript interfaces for type safety
- Located: `app/components/UserProfile.tsx`

### Section 2: State Management (20%)
✅ **Counter Component**
- Implements increment, decrement, and reset functionality
- Uses React state with proper state management
- Includes conditional rendering based on counter value
- Shows different messages and colors for positive/negative/zero values
- Located: `app/components/Counter.tsx`

### Section 3: Event Handling and Conditional Rendering (20%)
✅ **TodoList Component**
- Allows users to add and remove todo items
- Implements proper event handling for form submission
- Shows conditional rendering when list is empty
- Includes todo completion toggle functionality
- Displays statistics (total, completed, remaining)
- Located: `app/components/TodoList.tsx`

### Section 4: Routing (20%)
✅ **Next.js Routing Setup**
- Basic routing between Home and About pages
- Navigation menu with active state indication
- Responsive navigation for mobile and desktop
- Home page: `app/page.tsx`
- About page: `app/about/page.tsx`
- Navigation: `app/components/Navigation.tsx`

### Section 5: APIs and Data Fetching (20%)
✅ **JSONPlaceholder API Integration**
- Fetches data from `https://jsonplaceholder.typicode.com/posts`
- Implements proper loading states
- Includes comprehensive error handling with retry functionality
- Displays fetched data in a clean, organized format
- Located: `app/components/PostsList.tsx`

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository or navigate to the project directory
2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🛠️ Technologies Used

- **Next.js 15.4.6** - React framework with App Router
- **React 19.1.0** - UI library
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **JSONPlaceholder API** - Mock REST API for testing

## 📁 Project Structure

```
final_project/
├── app/
│   ├── components/
│   │   ├── UserProfile.tsx      # Section 1: Component Design
│   │   ├── Counter.tsx          # Section 2: State Management
│   │   ├── TodoList.tsx         # Section 3: Event Handling
│   │   ├── Navigation.tsx       # Section 4: Navigation
│   │   └── PostsList.tsx        # Section 5: API Data Fetching
│   ├── about/
│   │   └── page.tsx             # About page
│   ├── layout.tsx               # Root layout with navigation
│   ├── page.tsx                 # Home page showcasing all components
│   └── globals.css              # Global styles
├── public/
│   └── profile-placeholder.svg  # Profile image placeholder
└── README.md                    # This file
```

## 🎯 Features Demonstrated

### React Concepts
- Functional components with hooks
- State management with useState
- Effect handling with useEffect
- Event handling and form submission
- Conditional rendering
- Props and TypeScript interfaces

### Next.js Features
- App Router for navigation
- Client-side and server-side components
- Image optimization with next/image
- File-based routing

### Modern Development Practices
- TypeScript for type safety
- Responsive design with Tailwind CSS
- Error boundaries and loading states
- Clean component architecture
- Proper separation of concerns

## 📸 Screenshots and Code Documentation

Each component includes:
- Comprehensive TypeScript interfaces
- Proper error handling
- Loading states where applicable
- Responsive design
- Accessibility considerations
- Clean, readable code with comments

## 🧪 Testing the Application

To verify all functionality:

1. **UserProfile Component**: Check that the profile displays correctly with image, name, and bio
2. **Counter Component**: Test increment, decrement, and reset buttons; verify conditional messages
3. **TodoList Component**: Add todos, mark as complete, remove items, check empty state
4. **Navigation**: Navigate between Home and About pages
5. **API Integration**: Verify posts load correctly, test error handling by disconnecting internet

## 📝 Submission Notes

This project fulfills all requirements specified in the final exam instructions:
- ✅ Screenshots showcasing functionality (visible in browser)
- ✅ Source code files (.js/.jsx/.ts/.tsx) for each component
- ✅ Documentation explaining key implementation details
- ✅ All five sections implemented with proper functionality
- ✅ Clean, professional code following React best practices
