{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/about', label: 'About' }\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <h1 className=\"text-xl font-bold text-gray-800\">CPAN 144 Final</h1>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors ${\n                    pathname === item.href\n                      ? 'border-blue-500 text-gray-900'\n                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n          \n          {/* Mobile menu button */}\n          <div className=\"sm:hidden flex items-center\">\n            <div className=\"flex space-x-4\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    pathname === item.href\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,aAAuB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,oFAAoF,EAC9F,aAAa,KAAK,IAAI,GAClB,kCACA,8EACJ;kDAED,KAAK,KAAK;uCARN,KAAK,IAAI;;;;;;;;;;;;;;;;kCAetB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,8BACA,uDACJ;8CAED,KAAK,KAAK;mCARN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBhC;uCAEe", "debugId": null}}]}