'use client';

import React, { useState } from 'react';

interface TodoItem {
  id: number;
  text: string;
  completed: boolean;
}

const TodoList: React.FC = () => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [inputText, setInputText] = useState<string>('');

  const addTodo = () => {
    if (inputText.trim() !== '') {
      const newTodo: TodoItem = {
        id: Date.now(),
        text: inputText.trim(),
        completed: false
      };
      setTodos(prevTodos => [...prevTodos, newTodo]);
      setInputText('');
    }
  };

  const removeTodo = (id: number) => {
    setTodos(prevTodos => prevTodos.filter(todo => todo.id !== id));
  };

  const toggleComplete = (id: number) => {
    setTodos(prevTodos =>
      prevTodos.map(todo =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputText(e.target.value);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      addTodo();
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-md p-6 m-4">
      <h2 className="text-2xl font-bold text-center mb-4">Todo List</h2>
      
      {/* Input section */}
      <div className="flex mb-4">
        <input
          type="text"
          value={inputText}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="Add a new todo..."
          className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={addTodo}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r-md transition-colors"
        >
          Add
        </button>
      </div>

      {/* Conditional rendering for empty state */}
      {todos.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 text-6xl mb-4">📝</div>
          <p className="text-gray-500 text-lg">No todos yet!</p>
          <p className="text-gray-400 text-sm">Add your first todo above to get started.</p>
        </div>
      ) : (
        <div>
          {/* Todo list */}
          <ul className="space-y-2">
            {todos.map(todo => (
              <li
                key={todo.id}
                className={`flex items-center justify-between p-3 border rounded-md ${
                  todo.completed 
                    ? 'bg-gray-100 border-gray-300' 
                    : 'bg-white border-gray-200'
                }`}
              >
                <div className="flex items-center flex-1">
                  <input
                    type="checkbox"
                    checked={todo.completed}
                    onChange={() => toggleComplete(todo.id)}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span
                    className={`${
                      todo.completed 
                        ? 'line-through text-gray-500' 
                        : 'text-gray-900'
                    }`}
                  >
                    {todo.text}
                  </span>
                </div>
                <button
                  onClick={() => removeTodo(todo.id)}
                  className="ml-3 bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm transition-colors"
                >
                  Remove
                </button>
              </li>
            ))}
          </ul>

          {/* Todo statistics */}
          <div className="mt-4 text-sm text-gray-600 text-center">
            <p>
              Total: {todos.length} | 
              Completed: {todos.filter(todo => todo.completed).length} | 
              Remaining: {todos.filter(todo => !todo.completed).length}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TodoList;
