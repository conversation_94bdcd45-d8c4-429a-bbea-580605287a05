import React from 'react';
import UserProfile from './components/UserProfile';
import Counter from './components/Counter';
import TodoList from './components/TodoList';
import PostsList from './components/PostsList';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            CPAN 144 - Final Exam Project
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            This project demonstrates advanced front-end development concepts including
            component design, state management, event handling, routing, and API integration.
          </p>
        </div>

        {/* Section 1: Component Design and JSX */}
        <section className="mb-16">
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 text-center">
            Section 1: Component Design and JSX
          </h2>
          <UserProfile
            name="<PERSON>"
            profilePicture="/profile-placeholder.svg"
            bio="A passionate developer who loves creating amazing user experiences with React and Next.js. Always eager to learn new technologies and share knowledge with the community."
          />
        </section>

        {/* Section 2: State Management */}
        <section className="mb-16">
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 text-center">
            Section 2: State Management
          </h2>
          <Counter />
        </section>

        {/* Section 3: Event Handling and Conditional Rendering */}
        <section className="mb-16">
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 text-center">
            Section 3: Event Handling and Conditional Rendering
          </h2>
          <TodoList />
        </section>

        {/* Section 5: APIs and Data Fetching */}
        <section className="mb-16">
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 text-center">
            Section 5: APIs and Data Fetching
          </h2>
          <PostsList />
        </section>

        {/* Footer */}
        <footer className="text-center py-8 border-t border-gray-200">
          <p className="text-gray-600">
            CPAN 144 - Advanced Front-End Programming | Final Exam Project
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Built with Next.js, React, TypeScript, and Tailwind CSS
          </p>
        </footer>
      </div>
    </div>
  );
}
