'use client';

import React, { useState, useEffect } from 'react';

interface Post {
  id: number;
  title: string;
  body: string;
  userId: number;
}

const PostsList: React.FC = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('https://jsonplaceholder.typicode.com/posts');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data: Post[] = await response.json();
        
        // Limit to first 10 posts for better display
        setPosts(data.slice(0, 10));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  const retryFetch = () => {
    setError(null);
    setLoading(true);
    // Re-trigger the useEffect by updating a dependency
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4">
        <h2 className="text-2xl font-bold text-center mb-6">Posts from JSONPlaceholder API</h2>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-600">Loading posts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4">
        <h2 className="text-2xl font-bold text-center mb-6">Posts from JSONPlaceholder API</h2>
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-red-600 mb-2">Error Loading Posts</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={retryFetch}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-md p-6 m-4">
      <h2 className="text-2xl font-bold text-center mb-6">Posts from JSONPlaceholder API</h2>
      
      <div className="mb-4 text-center">
        <span className="bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">
          ✅ Successfully loaded {posts.length} posts
        </span>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {posts.map((post) => (
          <div
            key={post.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between mb-2">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Post #{post.id}
              </span>
              <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                User {post.userId}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2 capitalize">
              {post.title}
            </h3>
            
            <p className="text-gray-600 text-sm leading-relaxed">
              {post.body}
            </p>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center">
        <button
          onClick={retryFetch}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Refresh Posts
        </button>
      </div>
    </div>
  );
};

export default PostsList;
