'use client';

import React, { useState } from 'react';

const Counter: React.FC = () => {
  const [count, setCount] = useState<number>(0);

  const increment = () => {
    setCount(prevCount => prevCount + 1);
  };

  const decrement = () => {
    setCount(prevCount => prevCount - 1);
  };

  const reset = () => {
    setCount(0);
  };

  // Conditional rendering based on counter value
  const getCounterMessage = () => {
    if (count === 0) {
      return "Counter is at zero";
    } else if (count > 0) {
      return `Counter is positive: ${count}`;
    } else {
      return `Counter is negative: ${count}`;
    }
  };

  const getCounterColor = () => {
    if (count === 0) {
      return "text-gray-600";
    } else if (count > 0) {
      return "text-green-600";
    } else {
      return "text-red-600";
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-md p-6 m-4">
      <h2 className="text-2xl font-bold text-center mb-4">Counter Component</h2>
      
      <div className="text-center mb-4">
        <div className={`text-4xl font-bold mb-2 ${getCounterColor()}`}>
          {count}
        </div>
        <p className={`text-lg ${getCounterColor()}`}>
          {getCounterMessage()}
        </p>
      </div>

      <div className="flex justify-center space-x-4">
        <button
          onClick={decrement}
          className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Decrement
        </button>
        <button
          onClick={reset}
          className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Reset
        </button>
        <button
          onClick={increment}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors"
        >
          Increment
        </button>
      </div>

      {/* Additional conditional rendering for special values */}
      {count === 10 && (
        <div className="mt-4 p-2 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
          🎉 You've reached 10! Great job!
        </div>
      )}
      
      {count === -10 && (
        <div className="mt-4 p-2 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          ❄️ You've reached -10! That's quite low!
        </div>
      )}
    </div>
  );
};

export default Counter;
