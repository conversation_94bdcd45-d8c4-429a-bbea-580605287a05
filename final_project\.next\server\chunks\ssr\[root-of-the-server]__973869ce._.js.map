{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/UserProfile.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface UserProfileProps {\n  name: string;\n  profilePicture: string;\n  bio: string;\n}\n\nconst UserProfile: React.FC<UserProfileProps> = ({ name, profilePicture, bio }) => {\n  return (\n    <div className=\"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4\">\n      <div className=\"md:flex\">\n        <div className=\"md:shrink-0\">\n          <Image\n            className=\"h-48 w-full object-cover md:h-full md:w-48\"\n            src={profilePicture}\n            alt={`${name}'s profile picture`}\n            width={192}\n            height={192}\n            priority\n          />\n        </div>\n        <div className=\"p-8\">\n          <div className=\"uppercase tracking-wide text-sm text-indigo-500 font-semibold\">\n            User Profile\n          </div>\n          <h2 className=\"block mt-1 text-lg leading-tight font-medium text-black\">\n            {name}\n          </h2>\n          <p className=\"mt-2 text-slate-500\">\n            {bio}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UserProfile;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE;IAC5E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAK;wBACL,KAAK,GAAG,KAAK,kBAAkB,CAAC;wBAChC,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;;;;;;8BAGZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgE;;;;;;sCAG/E,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAEH,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/Counter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/Counter.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/Counter.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/Counter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/Counter.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/Counter.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/TodoList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/TodoList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/TodoList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/TodoList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/TodoList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/TodoList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/PostsList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/PostsList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/PostsList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/components/PostsList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/PostsList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/PostsList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Computer%20Programming/Sem%202/Adv.%20Front-End%20Programming/Final%20Exam/final_project/app/page.tsx"], "sourcesContent": ["import React from 'react';\nimport UserProfile from './components/UserProfile';\nimport Counter from './components/Counter';\nimport TodoList from './components/TodoList';\nimport PostsList from './components/PostsList';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            CPAN 144 - Final Exam Project\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            This project demonstrates advanced front-end development concepts including\n            component design, state management, event handling, routing, and API integration.\n          </p>\n        </div>\n\n        {/* Section 1: Component Design and JSX */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-semibold text-gray-800 mb-6 text-center\">\n            Section 1: Component Design and JSX\n          </h2>\n          <UserProfile\n            name=\"<PERSON>\"\n            profilePicture=\"/profile-placeholder.svg\"\n            bio=\"A passionate developer who loves creating amazing user experiences with React and Next.js. Always eager to learn new technologies and share knowledge with the community.\"\n          />\n        </section>\n\n        {/* Section 2: State Management */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-semibold text-gray-800 mb-6 text-center\">\n            Section 2: State Management\n          </h2>\n          <Counter />\n        </section>\n\n        {/* Section 3: Event Handling and Conditional Rendering */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-semibold text-gray-800 mb-6 text-center\">\n            Section 3: Event Handling and Conditional Rendering\n          </h2>\n          <TodoList />\n        </section>\n\n        {/* Section 5: APIs and Data Fetching */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-semibold text-gray-800 mb-6 text-center\">\n            Section 5: APIs and Data Fetching\n          </h2>\n          <PostsList />\n        </section>\n\n        {/* Footer */}\n        <footer className=\"text-center py-8 border-t border-gray-200\">\n          <p className=\"text-gray-600\">\n            CPAN 144 - Advanced Front-End Programming | Final Exam Project\n          </p>\n          <p className=\"text-sm text-gray-500 mt-2\">\n            Built with Next.js, React, TypeScript, and Tailwind CSS\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC,iIAAA,CAAA,UAAW;4BACV,MAAK;4BACL,gBAAe;4BACf,KAAI;;;;;;;;;;;;8BAKR,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;8BAIV,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC,8HAAA,CAAA,UAAQ;;;;;;;;;;;8BAIX,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;8BAIZ,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}]}